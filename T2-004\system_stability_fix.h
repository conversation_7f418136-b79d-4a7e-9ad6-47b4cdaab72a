/**
 * @file system_stability_fix.h
 * @brief 系统稳定性修复方案头文件
 * @details 解决中断、延时、初始化导致的系统堵塞和OLED黑屏问题
 * <AUTHOR> Assistant
 * @date 2025-01-01
 */

#ifndef __SYSTEM_STABILITY_FIX_H
#define __SYSTEM_STABILITY_FIX_H

#include "SysConfig.h"

/* ========================= 配置参数 ========================= */
#define I2C_ENHANCED_TIMEOUT_MS     (50)    // 增强的I2C超时时间
#define I2C_RETRY_COUNT            (3)      // I2C重试次数
#define TASK_WATCHDOG_TIMEOUT_MS   (100)    // 任务看门狗超时时间
#define CRITICAL_SECTION_TIMEOUT   (20)     // 临界区最大执行时间(ms)

/* ========================= 系统状态枚举 ========================= */
typedef enum {
    SYS_STATE_NORMAL = 0,       // 正常状态
    SYS_STATE_I2C_ERROR,        // I2C通信错误
    SYS_STATE_TASK_TIMEOUT,     // 任务超时
    SYS_STATE_INTERRUPT_ERROR,  // 中断异常
    SYS_STATE_RECOVERY          // 恢复状态
} system_state_t;

/* ========================= I2C增强功能 ========================= */
typedef struct {
    bool is_locked;             // I2C总线是否锁定
    uint32_t last_error_time;   // 上次错误时间
    uint8_t error_count;        // 错误计数
    uint8_t retry_count;        // 重试计数
} i2c_status_t;

/* ========================= 任务监控结构 ========================= */
typedef struct {
    uint32_t start_time;        // 任务开始时间
    uint32_t max_execution_time; // 最大执行时间
    bool is_running;            // 是否正在运行
    uint8_t timeout_count;      // 超时计数
} task_monitor_t;

/* ========================= 系统监控结构 ========================= */
typedef struct {
    system_state_t current_state;      // 当前系统状态
    uint32_t last_heartbeat;           // 上次心跳时间
    bool interrupt_enabled;            // 中断是否使能
    i2c_status_t i2c_oled_status;      // OLED I2C状态
    i2c_status_t i2c_mpu6050_status;   // MPU6050 I2C状态
    task_monitor_t task_monitors[MAXTASKS]; // 任务监控
} system_monitor_t;

/* ========================= 全局变量声明 ========================= */
extern system_monitor_t g_system_monitor;
extern volatile bool g_critical_section_flag;

/* ========================= 函数声明 ========================= */

// 系统监控初始化
void SystemMonitor_Init(void);

// 系统状态更新
void SystemMonitor_Update(void);

// 系统心跳
void SystemMonitor_Heartbeat(void);

// I2C增强功能
bool I2C_Enhanced_Write(void* i2c_inst, uint8_t addr, uint8_t* data, uint8_t len);
bool I2C_Enhanced_Read(void* i2c_inst, uint8_t addr, uint8_t reg, uint8_t* data, uint8_t len);
void I2C_Enhanced_Recovery(void* i2c_inst, i2c_status_t* status);
bool I2C_Enhanced_IsHealthy(i2c_status_t* status);

// 任务监控功能
void TaskMonitor_Start(uint8_t task_id);
void TaskMonitor_End(uint8_t task_id);
bool TaskMonitor_CheckTimeout(uint8_t task_id);
void TaskMonitor_Reset(uint8_t task_id);

// 中断安全功能
void InterruptSafe_Enter(void);
void InterruptSafe_Exit(void);
bool InterruptSafe_IsInCriticalSection(void);

// 系统恢复功能
void SystemRecovery_I2C(void);
void SystemRecovery_Tasks(void);
void SystemRecovery_Interrupts(void);
void SystemRecovery_Full(void);

// OLED增强功能
bool OLED_Enhanced_Init(void);
bool OLED_Enhanced_Write(uint8_t dat, uint8_t mode);
bool OLED_Enhanced_IsHealthy(void);
void OLED_Enhanced_Recovery(void);

// 延时增强功能
void Delay_Enhanced(uint32_t ms);
void Delay_Interruptible(uint32_t ms);
bool Delay_WithTimeout(uint32_t ms, bool (*condition)(void));

// 系统诊断功能
void SystemDiag_PrintStatus(void);
void SystemDiag_PrintI2CStatus(void);
void SystemDiag_PrintTaskStatus(void);

// 错误处理
void ErrorHandler_I2C(i2c_status_t* status);
void ErrorHandler_Task(uint8_t task_id);
void ErrorHandler_System(system_state_t error_state);

/* ========================= 宏定义 ========================= */
#define ENTER_CRITICAL_SECTION()   InterruptSafe_Enter()
#define EXIT_CRITICAL_SECTION()    InterruptSafe_Exit()

#define I2C_SAFE_WRITE(inst, addr, data, len) \
    I2C_Enhanced_Write(inst, addr, data, len)

#define I2C_SAFE_READ(inst, addr, reg, data, len) \
    I2C_Enhanced_Read(inst, addr, reg, data, len)

#define TASK_MONITOR_START(id)      TaskMonitor_Start(id)
#define TASK_MONITOR_END(id)        TaskMonitor_End(id)

#define SYSTEM_HEARTBEAT()          SystemMonitor_Heartbeat()

#endif /* __SYSTEM_STABILITY_FIX_H */
