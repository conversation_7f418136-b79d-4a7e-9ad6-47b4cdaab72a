******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Aug  1 15:34:39 2025

OUTPUT FILE NAME:   <TI_CAR1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00007c0d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  0000a238  00015dc8  R  X
  SRAM                  20200000   00008000  00000708  000078f8  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    0000a238   0000a238    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000088d0   000088d0    r-x .text
  00008990    00008990    00001830   00001830    r-- .rodata
  0000a1c0    0000a1c0    00000078   00000078    r-- .cinit
20200000    20200000    00000509   00000000    rw-
  20200000    20200000    000003d3   00000000    rw- .bss
  202003d4    202003d4    00000135   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000088d0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  00001364    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  0000159c    0000022c     MPU6050.o (.text.Read_Quad)
                  000017c8    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  000019f4    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001c14    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00001e08    000001e0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001fe8    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  000021c4    000001b0     Task.o (.text.Task_Start)
                  00002374    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  00002514    00000198     Task_App.o (.text.Task_Init)
                  000026ac    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000283e    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002840    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  000029c8    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  00002b50    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00002cc8    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00002e38    00000154     MPU6050.o (.text.MPU6050_Init)
                  00002f8c    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  000030c8    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  000031fc    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00003330    00000130     OLED.o (.text.OLED_ShowChar)
                  00003460    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  00003590    00000128     Task_App.o (.text.Task_Tracker)
                  000036b8    00000128     inv_mpu.o (.text.mpu_init)
                  000037e0    00000124     PID_IQMath.o (.text.PID_IQ_Prosc)
                  00003904    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  00003a28    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00003b48    00000118     OLED.o (.text.OLED_Init)
                  00003c60    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003d6c    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00003e74    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00003f78    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00004078    000000f0     Motor.o (.text.Motor_SetDirc)
                  00004168    000000f0     Task_App.o (.text.Task_Motor_PID)
                  00004258    000000ec     Task_App.o (.text.Task_OLED)
                  00004344    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  00004430    000000e4     Interrupt.o (.text.GROUP1_IRQHandler)
                  00004514    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000045f8    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  000046dc    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000047b8    000000dc     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  00004894    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  0000496c    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00004a44    000000d4     inv_mpu.o (.text.set_int_enable)
                  00004b18    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  00004be8    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  00004cac    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00004d70    000000bc     Task_App.o (.text.System_SoftReset)
                  00004e2c    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  00004ee8    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  00004fa0    000000b4     Task.o (.text.Task_Add)
                  00005054    000000ac     Task_App.o (.text.Task_Serial)
                  00005100    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  000051ac    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  00005258    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  00005302    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00005304    000000a2                            : udivmoddi4.S.obj (.text)
                  000053a6    00000002     --HOLE-- [fill = 0]
                  000053a8    000000a0     Motor.o (.text.Motor_SetDuty)
                  00005448    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000054e8    0000009c     OLED.o (.text.OLED_IsPresent)
                  00005584    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  00005620    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  000056b8    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  00005750    00000096     MPU6050.o (.text.inv_row_2_scale)
                  000057e6    00000002     --HOLE-- [fill = 0]
                  000057e8    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_Motor_PWM_init)
                  00005874    0000008c     Task_App.o (.text.Task_Key)
                  00005900    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  0000598c    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00005a18    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00005a9c    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00005b20    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00005ba2    00000002     --HOLE-- [fill = 0]
                  00005ba4    00000080     Motor.o (.text.Motor_GetSpeed)
                  00005c24    00000080     Task.o (.text.Task_Resume)
                  00005ca4    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00005d20    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00005d94    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00005da0    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00005e14    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  00005e88    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00005efa    00000002     --HOLE-- [fill = 0]
                  00005efc    00000070     Serial.o (.text.MyPrintf_DMA)
                  00005f6c    0000006e     OLED.o (.text.OLED_ShowString)
                  00005fda    00000002     --HOLE-- [fill = 0]
                  00005fdc    0000006c     Motor.o (.text.Motor_Start)
                  00006048    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  000060b4    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  0000611e    00000002     --HOLE-- [fill = 0]
                  00006120    00000068     Task.o (.text.Task_Suspend)
                  00006188    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000061f0    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00006256    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  000062bc    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00006320    00000064     MPU6050.o (.text.MPU6050_IsPresent)
                  00006384    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  000063e8    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000644a    00000002     --HOLE-- [fill = 0]
                  0000644c    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  000064ae    00000002     --HOLE-- [fill = 0]
                  000064b0    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00006510    00000060     Key_Led.o (.text.Key_Read)
                  00006570    00000060     Task_App.o (.text.Task_IdleFunction)
                  000065d0    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  00006630    00000060     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00006690    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  000066f0    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  0000674e    00000002     --HOLE-- [fill = 0]
                  00006750    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000067ac    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00006808    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  00006864    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  000068c0    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00006918    00000058     Serial.o (.text.Serial_Init)
                  00006970    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  000069c8    00000058            : _printfi.c.obj (.text._pconv_f)
                  00006a20    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00006a76    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00006ac8    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  00006b18    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  00006b68    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00006bb8    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  00006c04    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00006c50    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00006c9c    0000004c     OLED.o (.text.OLED_Printf)
                  00006ce8    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  00006d34    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00006d7e    00000002     --HOLE-- [fill = 0]
                  00006d80    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00006dca    00000002     --HOLE-- [fill = 0]
                  00006dcc    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00006e14    00000048     ADC.o (.text.adc_getValue)
                  00006e5c    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  00006ea4    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  00006eec    00000048     MPU6050.o (.text.mspm0_i2c_disable)
                  00006f34    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00006f78    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  00006fbc    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  00007000    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  00007044    00000044     OLED.o (.text.mspm0_i2c_disable)
                  00007088    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  000070ca    00000002     --HOLE-- [fill = 0]
                  000070cc    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  0000710e    00000002     --HOLE-- [fill = 0]
                  00007110    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00007150    00000040     Interrupt.o (.text.Interrupt_Init)
                  00007190    00000040     Task_App.o (.text.Task_GraySensor)
                  000071d0    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00007210    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00007250    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00007290    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  000072d0    0000003e     Task.o (.text.Task_CMP)
                  0000730e    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  0000734c    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00007388    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000073c4    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00007400    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  0000743c    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00007478    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  000074b4    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  000074f0    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  0000752c    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00007568    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000075a4    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  000075de    00000002     --HOLE-- [fill = 0]
                  000075e0    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  0000761a    00000038     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Normalize_For_User)
                  00007652    00000002     --HOLE-- [fill = 0]
                  00007654    00000038     Task_App.o (.text.Task_LED)
                  0000768c    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  000076c4    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000076f8    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  0000772c    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00007760    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  00007794    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  000077c6    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  000077f8    00000030     ADC.o (.text.DL_ADC12_getMemResult)
                  00007828    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00007858    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00007888    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  000078b8    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  000078e8    00000030            : vsnprintf.c.obj (.text._outs)
                  00007918    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  00007948    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  00007978    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000079a4    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  000079d0    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  000079fc    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00007a28    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  00007a52    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  00007a7a    00000028     OLED.o (.text.DL_Common_updateReg)
                  00007aa2    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00007aca    00000002     --HOLE-- [fill = 0]
                  00007acc    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  00007af4    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  00007b1c    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00007b44    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00007b6c    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00007b94    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00007bbc    00000028     SysTick.o (.text.SysTick_Increasment)
                  00007be4    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00007c0c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00007c34    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  00007c5a    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  00007c80    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00007ca6    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00007ccc    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00007cf0    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00007d14    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00007d38    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00007d5a    00000002     --HOLE-- [fill = 0]
                  00007d5c    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00007d7c    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00007d9c    00000020     SysTick.o (.text.Delay)
                  00007dbc    00000020     main.o (.text.main)
                  00007ddc    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  00007dfc    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00007e1a    00000002     --HOLE-- [fill = 0]
                  00007e1c    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00007e3a    00000002     --HOLE-- [fill = 0]
                  00007e3c    0000001c     ADC.o (.text.DL_ADC12_startConversion)
                  00007e58    0000001c     ADC.o (.text.DL_ADC12_stopConversion)
                  00007e74    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00007e90    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00007eac    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00007ec8    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00007ee4    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00007f00    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00007f1c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00007f38    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00007f54    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00007f70    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  00007f8c    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00007fa8    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00007fc4    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00007fe0    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00007ffc    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00008018    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00008034    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00008050    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  0000806c    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  00008088    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  000080a0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  000080b8    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  000080d0    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  000080e8    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00008100    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00008118    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00008130    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00008148    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  00008160    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00008178    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00008190    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  000081a8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000081c0    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  000081d8    00000018     Motor.o (.text.DL_GPIO_setPins)
                  000081f0    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00008208    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00008220    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00008238    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00008250    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00008268    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  00008280    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00008298    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000082b0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  000082c8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  000082e0    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  000082f8    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00008310    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00008328    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  00008340    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  00008358    00000018     MPU6050.o (.text.DL_I2C_reset)
                  00008370    00000018     OLED.o (.text.DL_I2C_reset)
                  00008388    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  000083a0    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  000083b8    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  000083d0    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  000083e8    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00008400    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00008418    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00008430    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00008448    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00008460    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  00008478    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00008490    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  000084a8    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  000084c0    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  000084d8    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  000084f0    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00008508    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00008520    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  00008538    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  00008550    00000018            : vsprintf.c.obj (.text._outs)
                  00008568    00000016     ADC.o (.text.DL_ADC12_disableConversions)
                  0000857e    00000016     ADC.o (.text.DL_ADC12_enableConversions)
                  00008594    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  000085aa    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  000085c0    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  000085d6    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  000085ec    00000016     OLED.o (.text.DL_GPIO_readPins)
                  00008602    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  00008618    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  0000862e    00000016     SysTick.o (.text.SysGetTick)
                  00008644    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  0000865a    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  0000866e    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00008682    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00008696    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  000086aa    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  000086be    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  000086d2    00000002     --HOLE-- [fill = 0]
                  000086d4    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  000086e8    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  000086fc    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00008710    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00008724    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00008738    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  0000874c    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00008760    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00008774    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  00008788    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  0000879c    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000087b0    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000087c2    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000087d4    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000087e6    00000010     ADC.o (.text.DL_ADC12_getStatus)
                  000087f6    00000002     --HOLE-- [fill = 0]
                  000087f8    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00008808    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00008818    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00008828    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00008838    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  00008846    00000002     --HOLE-- [fill = 0]
                  00008848    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00008856    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00008864    0000000e     MPU6050.o (.text.tap_cb)
                  00008872    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00008880    0000000c     SysTick.o (.text.Sys_GetTick)
                  0000888c    0000000c     Task.o (.text.Task_CheckNum)
                  00008898    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000088a2    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  000088ac    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  000088bc    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  000088c6    00000002     --HOLE-- [fill = 0]
                  000088c8    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  000088d8    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  000088e2    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  000088ec    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  000088f6    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00008900    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00008910    0000000a     libc.a : vsprintf.c.obj (.text._outc)
                  0000891a    0000000a     MPU6050.o (.text.android_orient_cb)
                  00008924    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  0000892c    00000008     Interrupt.o (.text.SysTick_Handler)
                  00008934    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  0000893c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00008944    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  0000894a    00000002     --HOLE-- [fill = 0]
                  0000894c    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  0000895c    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00008962    00000006            : exit.c.obj (.text:abort)
                  00008968    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  0000896c    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00008970    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00008974    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00008978    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00008988    00000004            : pre_init.c.obj (.text._system_pre_init)
                  0000898c    00000004     --HOLE-- [fill = 0]

.cinit     0    0000a1c0    00000078     
                  0000a1c0    00000050     (.cinit..data.load) [load image, compression = lzss]
                  0000a210    0000000c     (__TI_handler_table)
                  0000a21c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  0000a224    00000010     (__TI_cinit_table)
                  0000a234    00000004     --HOLE-- [fill = 0]

.rodata    0    00008990    00001830     
                  00008990    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  00009586    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00009b76    00000228     OLED_Font.o (.rodata.asc2_0806)
                  00009d9e    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00009da0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00009ea1    00000007     Task_App.o (.rodata.str1.8896853068034818020.1)
                  00009ea8    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00009ee8    00000032     Task_App.o (.rodata.str1.5297265082290894444.1)
                  00009f1a    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00009f1c    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00009f44    00000028     inv_mpu.o (.rodata.test)
                  00009f6c    00000027     Task_App.o (.rodata.str1.11952760121962574671.1)
                  00009f93    00000025     Task_App.o (.rodata.str1.12980382611605970010.1)
                  00009fb8    00000024     Task_App.o (.rodata.str1.16301319874972139807.1)
                  00009fdc    00000022     Task_App.o (.rodata.str1.9092480108036065651.1)
                  00009ffe    0000001f     Task_App.o (.rodata.str1.7269909886346255147.1)
                  0000a01d    0000001e     inv_mpu.o (.rodata.reg)
                  0000a03b    00000001     --HOLE-- [fill = 0]
                  0000a03c    0000001c     Task_App.o (.rodata..L__const.System_SoftReset.task_names)
                  0000a058    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  0000a070    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  0000a088    00000014     Task_App.o (.rodata.str1.5017135634981511656.1)
                  0000a09c    00000014     Task_App.o (.rodata.str1.5161480910995489644.1)
                  0000a0b0    00000014     Task_App.o (.rodata.str1.7346008805793267871.1)
                  0000a0c4    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  0000a0d5    00000011     Task_App.o (.rodata.str1.11373919790952722939.1)
                  0000a0e6    00000011     libc.a : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  0000a0f7    00000011     Task_App.o (.rodata.str1.4133793139133961309.1)
                  0000a108    00000010     Task_App.o (.rodata.str1.3850258909703972507.1)
                  0000a118    0000000e     Task_App.o (.rodata.str1.13861004553356644102.1)
                  0000a126    0000000d     Task_App.o (.rodata.str1.5883415095785080416.1)
                  0000a133    00000001     --HOLE-- [fill = 0]
                  0000a134    0000000c     inv_mpu.o (.rodata.hw)
                  0000a140    0000000c     Task_App.o (.rodata.str1.1200745476254391468.1)
                  0000a14c    0000000c     Task_App.o (.rodata.str1.13166305789289702848.1)
                  0000a158    0000000c     Task_App.o (.rodata.str1.14074990341397557290.1)
                  0000a164    0000000b     Task_App.o (.rodata.str1.4769078833470683459.1)
                  0000a16f    0000000b     Task_App.o (.rodata.str1.492715258893803702.1)
                  0000a17a    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  0000a184    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  0000a18c    00000008     ti_msp_dl_config.o (.rodata.gMotor_PWMConfig)
                  0000a194    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  0000a19c    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  0000a1a2    00000005     Task_App.o (.rodata.str1.11683036942922059812.1)
                  0000a1a7    00000004     Task_App.o (.rodata.str1.10635198597896025474.1)
                  0000a1ab    00000004     Task_App.o (.rodata.str1.16020955549137178199.1)
                  0000a1af    00000003     ti_msp_dl_config.o (.rodata.gMotor_PWMClockConfig)
                  0000a1b2    00000003     Task_App.o (.rodata.str1.7950429023856218820.1)
                  0000a1b5    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  0000a1b7    00000009     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000003d3     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000b0     (.common:GraySensor)
                  202003a0    00000010     (.common:quat)
                  202003b0    00000006     (.common:Data_Accel)
                  202003b6    00000006     (.common:Data_Gyro)
                  202003bc    00000004     (.common:Data_Pitch)
                  202003c0    00000004     (.common:Data_Roll)
                  202003c4    00000004     (.common:Data_Yaw)
                  202003c8    00000004     (.common:ExISR_Flag)
                  202003cc    00000004     (.common:sensor_timestamp)
                  202003d0    00000002     (.common:sensors)
                  202003d2    00000001     (.common:more)

.data      0    202003d4    00000135     UNINITIALIZED
                  202003d4    00000048     Motor.o (.data.Motor_Left)
                  2020041c    00000048     Motor.o (.data.Motor_Right)
                  20200464    0000002c     inv_mpu.o (.data.st)
                  20200490    00000010     Task_App.o (.data.Gray_Anolog)
                  202004a0    00000010     Task_App.o (.data.Gray_Normal)
                  202004b0    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  202004c0    0000000e     MPU6050.o (.data.hal)
                  202004ce    00000009     MPU6050.o (.data.gyro_orientation)
                  202004d7    00000008     Task_App.o (.data.Data_Tracker_Input)
                  202004df    00000001     Task_App.o (.data.Flag_LED)
                  202004e0    00000008     Task_App.o (.data.Motor)
                  202004e8    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202004ec    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202004f0    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202004f4    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004f8    00000004     SysTick.o (.data.delayTick)
                  202004fc    00000004     SysTick.o (.data.uwTick)
                  20200500    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  20200502    00000002     Task_App.o (.data.Task_Key.Key_Press_Count)
                  20200504    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  20200505    00000001     Task_App.o (.data.Gray_Digtal)
                  20200506    00000001     Task_App.o (.data.Task_Key.Key_Old)
                  20200507    00000001     Task.o (.data.Task_Num)
                  20200508    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               3426    123       0      
       startup_mspm0g350x_ticlang.o     8       192       0      
       main.o                           32      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3466    315       0      
                                                                 
    .\APP\Src\
       Task_App.o                       1940    487       243    
       Interrupt.o                      470     0         6      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2410    487       249    
                                                                 
    .\BSP\Src\
       MPU6050.o                        2584    0         70     
       OLED_Font.o                      0       2072      0      
       OLED.o                           2018    0         0      
       No_Mcu_Ganv_Grayscale_Sensor.o   1244    0         0      
       Task.o                           918     0         241    
       Serial.o                         404     0         512    
       Motor.o                          704     0         144    
       PID_IQMath.o                     402     0         0      
       ADC.o                            236     0         0      
       Key_Led.o                        118     0         0      
       SysTick.o                        106     0         8      
    +--+--------------------------------+-------+---------+---------+
       Total:                           8734    2072      975    
                                                                 
    .\DMP\
       inv_mpu_dmp_motion_driver.o      3110    3062      16     
       inv_mpu.o                        4600    82        44     
    +--+--------------------------------+-------+---------+---------+
       Total:                           7710    3144      60     
                                                                 
    C:/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o     388     0         0      
       dl_timer.o                       356     0         0      
       dl_i2c.o                         192     0         0      
       dl_uart.o                        90      0         0      
       dl_dma.o                         76      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1176    0         0      
                                                                 
    C:/ti/mspm0_sdk_2_04_00_06/source/ti/iqmath/lib/ticlang/m0p/mathacl/mspm0g1x0x_g3x0x/iqmath.a
       _IQNtoF.o                        48      0         0      
       _IQNdiv.o                        24      0         0      
       _IQNmpy.o                        24      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           96      0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                   4510    34        0      
       e_asin.c.obj                     908     0         0      
       s_atan.c.obj                     784     64        0      
       e_atan2.c.obj                    392     0         0      
       e_sqrt.c.obj                     368     0         0      
       qsort.c.obj                      308     0         0      
       aeabi_ctype.S.obj                0       257       0      
       s_scalbn.c.obj                   216     0         0      
       vsnprintf.c.obj                  136     0         0      
       copy_decompress_lzss.c.obj       124     0         0      
       s_frexp.c.obj                    92      0         0      
       _ltoa.c.obj                      88      0         0      
       vsprintf.c.obj                   78      0         0      
       atoi.c.obj                       64      0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       memccpy.c.obj                    34      0         0      
       memcmp.c.obj                     32      0         0      
       copy_zero_init.c.obj             22      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       wcslen.c.obj                     16      0         0      
       memset16.S.obj                   14      0         0      
       aeabi_portable.c.obj             8       0         4      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           8356    355       4      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     434     0         0      
       divdf3.S.obj                     284     0         0      
       muldf3.S.obj                     244     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       udivmoddi4.S.obj                 162     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       truncdfsf2.S.obj                 116     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       aeabi_idivmod.S.obj              86      0         0      
       fixdfsi.S.obj                    74      0         0      
       fixunsdfsi.S.obj                 66      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       extendsfdf2.S.obj                64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsisf.S.obj                40      0         0      
       floatunsidf.S.obj                36      0         0      
       muldi3.S.obj                     36      0         0      
       ashldi3.S.obj                    30      0         0      
       aeabi_uldivmod.S.obj             20      0         0      
       aeabi_memset.S.obj               14      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3020    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       116       0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     34972   6489      1800   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 0000a224 records: 2, size/record: 8, table size: 16
	.data: load addr=0000a1c0, load size=00000050 bytes, run addr=202003d4, run size=00000135 bytes, compression=lzss
	.bss: load addr=0000a21c, load size=00000008 bytes, run addr=20200000, run size=000003d3 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 0000a210 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   000026ad     000088ac     000088aa   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00004515     000088c8     000088c4   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             000088e0          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             000088f4          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             0000892a          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00008960          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00003c61     00008900     000088fe   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   000026b7     0000894c     00008948   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00008972          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00007c0d     00008978     00008974   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00008969  ADC0_IRQHandler                      
00008969  ADC1_IRQHandler                      
00008969  AES_IRQHandler                       
0000896c  C$$EXIT                              
00008969  CANFD0_IRQHandler                    
00008969  DAC0_IRQHandler                      
00007111  DL_ADC12_setClockConfig              
00008899  DL_Common_delayCycles                
00006c05  DL_DMA_initChannel                   
000066f1  DL_I2C_fillControllerTXFIFO          
00007401  DL_I2C_flushControllerTXFIFO         
00007ca7  DL_I2C_setClockConfig                
000046dd  DL_SYSCTL_configSYSPLL               
000062bd  DL_SYSCTL_setHFCLKSourceHFXTParams   
00006f35  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003e75  DL_Timer_initFourCCPWMMode           
00008019  DL_Timer_setCaptCompUpdateMethod     
00008431  DL_Timer_setCaptureCompareOutCtl     
00008809  DL_Timer_setCaptureCompareValue      
00008035  DL_Timer_setClockConfig              
00006dcd  DL_UART_init                         
000087b1  DL_UART_setClockConfig               
00008969  DMA_IRQHandler                       
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202004e8  Data_MotorEncoder                    
202004ec  Data_Motor_TarSpeed                  
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202004d7  Data_Tracker_Input                   
202004f0  Data_Tracker_Offset                  
202003c4  Data_Yaw                             
00008969  Default_Handler                      
00007d9d  Delay                                
202003c8  ExISR_Flag                           
202004df  Flag_LED                             
20200504  Flag_MPU6050_Ready                   
00008969  GROUP0_IRQHandler                    
00004431  GROUP1_IRQHandler                    
000047b9  Get_Analog_value                     
00007479  Get_Anolog_Value                     
00008839  Get_Digtal_For_User                  
0000761b  Get_Normalize_For_User               
202002f0  GraySensor                           
20200490  Gray_Anolog                          
20200505  Gray_Digtal                          
202004a0  Gray_Normal                          
0000896d  HOSTexit                             
00008969  HardFault_Handler                    
00008969  I2C0_IRQHandler                      
00008969  I2C1_IRQHandler                      
000060b5  I2C_OLED_Clear                       
000074b5  I2C_OLED_Set_Pos                     
00005621  I2C_OLED_WR_Byte                     
000064b1  I2C_OLED_i2c_sda_unlock              
00007151  Interrupt_Init                       
00006511  Key_Read                             
00002e39  MPU6050_Init                         
00006321  MPU6050_IsPresent                    
202004e0  Motor                                
00005ba5  Motor_GetSpeed                       
202003d4  Motor_Left                           
2020041c  Motor_Right                          
000053a9  Motor_SetDuty                        
00005fdd  Motor_Start                          
00005efd  MyPrintf_DMA                         
00008969  NMI_Handler                          
00002841  No_MCU_Ganv_Sensor_Init              
00005e89  No_MCU_Ganv_Sensor_Init_Frist        
00007089  No_Mcu_Ganv_Sensor_Task_Without_tick 
00003b49  OLED_Init                            
000054e9  OLED_IsPresent                       
00006c9d  OLED_Printf                          
00003331  OLED_ShowChar                        
00005f6d  OLED_ShowString                      
00007a29  PID_IQ_Init                          
000037e1  PID_IQ_Prosc                         
00006f79  PID_IQ_SetParams                     
00008969  PendSV_Handler                       
00008969  RTC_IRQHandler                       
0000159d  Read_Quad                            
00008975  Reset_Handler                        
00008969  SPI0_IRQHandler                      
00008969  SPI1_IRQHandler                      
00008969  SVC_Handler                          
00006ce9  SYSCFG_DL_ADC1_init                  
00007859  SYSCFG_DL_DMA_CH_RX_init             
000084f1  SYSCFG_DL_DMA_CH_TX_init             
00005d95  SYSCFG_DL_DMA_init                   
00001e09  SYSCFG_DL_GPIO_init                  
000068c1  SYSCFG_DL_I2C_MPU6050_init           
00006385  SYSCFG_DL_I2C_OLED_init              
000057e9  SYSCFG_DL_Motor_PWM_init             
00006751  SYSCFG_DL_SYSCTL_init                
00008819  SYSCFG_DL_SYSTICK_init               
00005a19  SYSCFG_DL_UART0_init                 
00007979  SYSCFG_DL_init                       
00005449  SYSCFG_DL_initPower                  
00006919  Serial_Init                          
20200000  Serial_RxData                        
0000862f  SysGetTick                           
0000892d  SysTick_Handler                      
00007bbd  SysTick_Increasment                  
00008881  Sys_GetTick                          
00004d71  System_SoftReset                     
00008969  TIMA0_IRQHandler                     
00008969  TIMA1_IRQHandler                     
00008969  TIMG0_IRQHandler                     
00008969  TIMG12_IRQHandler                    
00008969  TIMG6_IRQHandler                     
00008969  TIMG7_IRQHandler                     
00008969  TIMG8_IRQHandler                     
000087c3  TI_memcpy_small                      
00008873  TI_memset_small                      
00004fa1  Task_Add                             
0000888d  Task_CheckNum                        
00007191  Task_GraySensor                      
00006571  Task_IdleFunction                    
00002515  Task_Init                            
00005875  Task_Key                             
00007655  Task_LED                             
00004169  Task_Motor_PID                       
00004259  Task_OLED                            
00005c25  Task_Resume                          
00005055  Task_Serial                          
000021c5  Task_Start                           
00006121  Task_Suspend                         
00003591  Task_Tracker                         
00008969  UART0_IRQHandler                     
00008969  UART1_IRQHandler                     
00008969  UART2_IRQHandler                     
00008969  UART3_IRQHandler                     
00008509  _IQ24div                             
00008521  _IQ24mpy                             
00007889  _IQ24toF                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
0000a224  __TI_CINIT_Base                      
0000a234  __TI_CINIT_Limit                     
0000a234  __TI_CINIT_Warm                      
0000a210  __TI_Handler_Table_Base              
0000a21c  __TI_Handler_Table_Limit             
00007569  __TI_auto_init_nobinit_nopinit       
00005ca5  __TI_decompress_lzss                 
000087d5  __TI_decompress_none                 
00006971  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00008645  __TI_zero_init_nomemset              
000026b7  __adddf3                             
00004977  __addsf3                             
00009da0  __aeabi_ctype_table_                 
00009da0  __aeabi_ctype_table_C                
00005da1  __aeabi_d2f                          
00006d81  __aeabi_d2iz                         
000070cd  __aeabi_d2uiz                        
000026b7  __aeabi_dadd                         
000063e9  __aeabi_dcmpeq                       
00006425  __aeabi_dcmpge                       
00006439  __aeabi_dcmpgt                       
00006411  __aeabi_dcmple                       
000063fd  __aeabi_dcmplt                       
00003c61  __aeabi_ddiv                         
00004515  __aeabi_dmul                         
000026ad  __aeabi_dsub                         
202004f4  __aeabi_errno                        
00008935  __aeabi_errno_addr                   
00007211  __aeabi_f2d                          
0000768d  __aeabi_f2iz                         
00004977  __aeabi_fadd                         
0000644d  __aeabi_fcmpeq                       
00006489  __aeabi_fcmpge                       
0000649d  __aeabi_fcmpgt                       
00006475  __aeabi_fcmple                       
00006461  __aeabi_fcmplt                       
00005b21  __aeabi_fdiv                         
00005901  __aeabi_fmul                         
0000496d  __aeabi_fsub                         
000079d1  __aeabi_i2d                          
000074f1  __aeabi_i2f                          
00006a21  __aeabi_idiv                         
0000283f  __aeabi_idiv0                        
00006a21  __aeabi_idivmod                      
00005303  __aeabi_ldiv0                        
00007e1d  __aeabi_llsl                         
00007d15  __aeabi_lmul                         
0000893d  __aeabi_memcpy                       
0000893d  __aeabi_memcpy4                      
0000893d  __aeabi_memcpy8                      
00008849  __aeabi_memset                       
00008849  __aeabi_memset4                      
00008849  __aeabi_memset8                      
00007cf1  __aeabi_ui2d                         
00007be5  __aeabi_ui2f                         
000071d1  __aeabi_uidiv                        
000071d1  __aeabi_uidivmod                     
00008761  __aeabi_uldivmod                     
00007e1d  __ashldi3                            
ffffffff  __binit__                            
00006189  __cmpdf2                             
000075a5  __cmpsf2                             
00003c61  __divdf3                             
00005b21  __divsf3                             
00006189  __eqdf2                              
000075a5  __eqsf2                              
00007211  __extendsfdf2                        
00006d81  __fixdfsi                            
0000768d  __fixsfsi                            
000070cd  __fixunsdfsi                         
000079d1  __floatsidf                          
000074f1  __floatsisf                          
00007cf1  __floatunsidf                        
00007be5  __floatunsisf                        
00005d21  __gedf2                              
0000752d  __gesf2                              
00005d21  __gtdf2                              
0000752d  __gtsf2                              
00006189  __ledf2                              
000075a5  __lesf2                              
00006189  __ltdf2                              
000075a5  __ltsf2                              
UNDEFED   __mpu_init                           
00004515  __muldf3                             
00007d15  __muldi3                             
000075e1  __muldsi3                            
00005901  __mulsf3                             
00006189  __nedf2                              
000075a5  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000026ad  __subdf3                             
0000496d  __subsf3                             
00005da1  __truncdfsf2                         
00005305  __udivmoddi4                         
00007c0d  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00008989  _system_pre_init                     
00008963  abort                                
00006e15  adc_getValue                         
00009b76  asc2_0806                            
00009586  asc2_1608                            
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
000029c9  atan2                                
000029c9  atan2l                               
00000df5  atanl                                
00007251  atoi                                 
ffffffff  binit                                
00006049  convertAnalogToDigital               
202004f8  delayTick                            
00006e5d  dmp_enable_6x_lp_quat                
000010ed  dmp_enable_feature                   
000065d1  dmp_enable_gyro_cal                  
00006ea5  dmp_enable_lp_quat                   
0000806d  dmp_load_motion_driver_firmware      
00001c15  dmp_read_fifo                        
00008775  dmp_register_android_orient_cb       
00008789  dmp_register_tap_cb                  
000056b9  dmp_set_fifo_rate                    
00002b51  dmp_set_orientation                  
00006fbd  dmp_set_shake_reject_thresh          
00007795  dmp_set_shake_reject_time            
000077c7  dmp_set_shake_reject_timeout         
00006257  dmp_set_tap_axes                     
00007001  dmp_set_tap_count                    
00001365  dmp_set_tap_thresh                   
00007919  dmp_set_tap_time                     
00007949  dmp_set_tap_time_multi               
20200508  enable_group1_irq                    
000067ad  frexp                                
000067ad  frexpl                               
0000a134  hw                                   
00000000  interruptVectors                     
00004895  ldexp                                
00004895  ldexpl                               
00007dbd  main                                 
00007d39  memccpy                              
00007ddd  memcmp                               
202003d2  more                                 
00006631  mpu6050_i2c_sda_unlock               
00004e2d  mpu_configure_fifo                   
00005e15  mpu_get_accel_fsr                    
00006691  mpu_get_gyro_fsr                     
00007761  mpu_get_sample_rate                  
000036b9  mpu_init                             
00003905  mpu_load_firmware                    
00003f79  mpu_lp_accel_mode                    
00003d6d  mpu_read_fifo_stream                 
00005101  mpu_read_mem                         
000017c9  mpu_reset_fifo                       
000045f9  mpu_set_accel_fsr                    
00002375  mpu_set_bypass                       
00004ee9  mpu_set_dmp_state                    
00004be9  mpu_set_gyro_fsr                     
00005585  mpu_set_int_latched                  
00004b19  mpu_set_lpf                          
00004345  mpu_set_sample_rate                  
00003461  mpu_set_sensors                      
000051ad  mpu_write_mem                        
000030c9  mspm0_i2c_read                       
00004cad  mspm0_i2c_write                      
00005259  normalizeAnalogValues                
000031fd  qsort                                
202003a0  quat                                 
0000a01d  reg                                  
00004895  scalbn                               
00004895  scalbnl                              
202003cc  sensor_timestamp                     
202003d0  sensors                              
00002cc9  sqrt                                 
00002cc9  sqrtl                                
00009f44  test                                 
202004fc  uwTick                               
00007291  vsnprintf                            
000079fd  vsprintf                             
00008829  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  dmp_enable_feature                   
00001365  dmp_set_tap_thresh                   
0000159d  Read_Quad                            
000017c9  mpu_reset_fifo                       
00001c15  dmp_read_fifo                        
00001e09  SYSCFG_DL_GPIO_init                  
000021c5  Task_Start                           
00002375  mpu_set_bypass                       
00002515  Task_Init                            
000026ad  __aeabi_dsub                         
000026ad  __subdf3                             
000026b7  __adddf3                             
000026b7  __aeabi_dadd                         
0000283f  __aeabi_idiv0                        
00002841  No_MCU_Ganv_Sensor_Init              
000029c9  atan2                                
000029c9  atan2l                               
00002b51  dmp_set_orientation                  
00002cc9  sqrt                                 
00002cc9  sqrtl                                
00002e39  MPU6050_Init                         
000030c9  mspm0_i2c_read                       
000031fd  qsort                                
00003331  OLED_ShowChar                        
00003461  mpu_set_sensors                      
00003591  Task_Tracker                         
000036b9  mpu_init                             
000037e1  PID_IQ_Prosc                         
00003905  mpu_load_firmware                    
00003b49  OLED_Init                            
00003c61  __aeabi_ddiv                         
00003c61  __divdf3                             
00003d6d  mpu_read_fifo_stream                 
00003e75  DL_Timer_initFourCCPWMMode           
00003f79  mpu_lp_accel_mode                    
00004169  Task_Motor_PID                       
00004259  Task_OLED                            
00004345  mpu_set_sample_rate                  
00004431  GROUP1_IRQHandler                    
00004515  __aeabi_dmul                         
00004515  __muldf3                             
000045f9  mpu_set_accel_fsr                    
000046dd  DL_SYSCTL_configSYSPLL               
000047b9  Get_Analog_value                     
00004895  ldexp                                
00004895  ldexpl                               
00004895  scalbn                               
00004895  scalbnl                              
0000496d  __aeabi_fsub                         
0000496d  __subsf3                             
00004977  __addsf3                             
00004977  __aeabi_fadd                         
00004b19  mpu_set_lpf                          
00004be9  mpu_set_gyro_fsr                     
00004cad  mspm0_i2c_write                      
00004d71  System_SoftReset                     
00004e2d  mpu_configure_fifo                   
00004ee9  mpu_set_dmp_state                    
00004fa1  Task_Add                             
00005055  Task_Serial                          
00005101  mpu_read_mem                         
000051ad  mpu_write_mem                        
00005259  normalizeAnalogValues                
00005303  __aeabi_ldiv0                        
00005305  __udivmoddi4                         
000053a9  Motor_SetDuty                        
00005449  SYSCFG_DL_initPower                  
000054e9  OLED_IsPresent                       
00005585  mpu_set_int_latched                  
00005621  I2C_OLED_WR_Byte                     
000056b9  dmp_set_fifo_rate                    
000057e9  SYSCFG_DL_Motor_PWM_init             
00005875  Task_Key                             
00005901  __aeabi_fmul                         
00005901  __mulsf3                             
00005a19  SYSCFG_DL_UART0_init                 
00005b21  __aeabi_fdiv                         
00005b21  __divsf3                             
00005ba5  Motor_GetSpeed                       
00005c25  Task_Resume                          
00005ca5  __TI_decompress_lzss                 
00005d21  __gedf2                              
00005d21  __gtdf2                              
00005d95  SYSCFG_DL_DMA_init                   
00005da1  __aeabi_d2f                          
00005da1  __truncdfsf2                         
00005e15  mpu_get_accel_fsr                    
00005e89  No_MCU_Ganv_Sensor_Init_Frist        
00005efd  MyPrintf_DMA                         
00005f6d  OLED_ShowString                      
00005fdd  Motor_Start                          
00006049  convertAnalogToDigital               
000060b5  I2C_OLED_Clear                       
00006121  Task_Suspend                         
00006189  __cmpdf2                             
00006189  __eqdf2                              
00006189  __ledf2                              
00006189  __ltdf2                              
00006189  __nedf2                              
00006257  dmp_set_tap_axes                     
000062bd  DL_SYSCTL_setHFCLKSourceHFXTParams   
00006321  MPU6050_IsPresent                    
00006385  SYSCFG_DL_I2C_OLED_init              
000063e9  __aeabi_dcmpeq                       
000063fd  __aeabi_dcmplt                       
00006411  __aeabi_dcmple                       
00006425  __aeabi_dcmpge                       
00006439  __aeabi_dcmpgt                       
0000644d  __aeabi_fcmpeq                       
00006461  __aeabi_fcmplt                       
00006475  __aeabi_fcmple                       
00006489  __aeabi_fcmpge                       
0000649d  __aeabi_fcmpgt                       
000064b1  I2C_OLED_i2c_sda_unlock              
00006511  Key_Read                             
00006571  Task_IdleFunction                    
000065d1  dmp_enable_gyro_cal                  
00006631  mpu6050_i2c_sda_unlock               
00006691  mpu_get_gyro_fsr                     
000066f1  DL_I2C_fillControllerTXFIFO          
00006751  SYSCFG_DL_SYSCTL_init                
000067ad  frexp                                
000067ad  frexpl                               
000068c1  SYSCFG_DL_I2C_MPU6050_init           
00006919  Serial_Init                          
00006971  __TI_ltoa                            
00006a21  __aeabi_idiv                         
00006a21  __aeabi_idivmod                      
00006c05  DL_DMA_initChannel                   
00006c9d  OLED_Printf                          
00006ce9  SYSCFG_DL_ADC1_init                  
00006d81  __aeabi_d2iz                         
00006d81  __fixdfsi                            
00006dcd  DL_UART_init                         
00006e15  adc_getValue                         
00006e5d  dmp_enable_6x_lp_quat                
00006ea5  dmp_enable_lp_quat                   
00006f35  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00006f79  PID_IQ_SetParams                     
00006fbd  dmp_set_shake_reject_thresh          
00007001  dmp_set_tap_count                    
00007089  No_Mcu_Ganv_Sensor_Task_Without_tick 
000070cd  __aeabi_d2uiz                        
000070cd  __fixunsdfsi                         
00007111  DL_ADC12_setClockConfig              
00007151  Interrupt_Init                       
00007191  Task_GraySensor                      
000071d1  __aeabi_uidiv                        
000071d1  __aeabi_uidivmod                     
00007211  __aeabi_f2d                          
00007211  __extendsfdf2                        
00007251  atoi                                 
00007291  vsnprintf                            
00007401  DL_I2C_flushControllerTXFIFO         
00007479  Get_Anolog_Value                     
000074b5  I2C_OLED_Set_Pos                     
000074f1  __aeabi_i2f                          
000074f1  __floatsisf                          
0000752d  __gesf2                              
0000752d  __gtsf2                              
00007569  __TI_auto_init_nobinit_nopinit       
000075a5  __cmpsf2                             
000075a5  __eqsf2                              
000075a5  __lesf2                              
000075a5  __ltsf2                              
000075a5  __nesf2                              
000075e1  __muldsi3                            
0000761b  Get_Normalize_For_User               
00007655  Task_LED                             
0000768d  __aeabi_f2iz                         
0000768d  __fixsfsi                            
00007761  mpu_get_sample_rate                  
00007795  dmp_set_shake_reject_time            
000077c7  dmp_set_shake_reject_timeout         
00007859  SYSCFG_DL_DMA_CH_RX_init             
00007889  _IQ24toF                             
00007919  dmp_set_tap_time                     
00007949  dmp_set_tap_time_multi               
00007979  SYSCFG_DL_init                       
000079d1  __aeabi_i2d                          
000079d1  __floatsidf                          
000079fd  vsprintf                             
00007a29  PID_IQ_Init                          
00007bbd  SysTick_Increasment                  
00007be5  __aeabi_ui2f                         
00007be5  __floatunsisf                        
00007c0d  _c_int00_noargs                      
00007ca7  DL_I2C_setClockConfig                
00007cf1  __aeabi_ui2d                         
00007cf1  __floatunsidf                        
00007d15  __aeabi_lmul                         
00007d15  __muldi3                             
00007d39  memccpy                              
00007d9d  Delay                                
00007dbd  main                                 
00007ddd  memcmp                               
00007e1d  __aeabi_llsl                         
00007e1d  __ashldi3                            
00008019  DL_Timer_setCaptCompUpdateMethod     
00008035  DL_Timer_setClockConfig              
0000806d  dmp_load_motion_driver_firmware      
00008431  DL_Timer_setCaptureCompareOutCtl     
000084f1  SYSCFG_DL_DMA_CH_TX_init             
00008509  _IQ24div                             
00008521  _IQ24mpy                             
0000862f  SysGetTick                           
00008645  __TI_zero_init_nomemset              
00008761  __aeabi_uldivmod                     
00008775  dmp_register_android_orient_cb       
00008789  dmp_register_tap_cb                  
000087b1  DL_UART_setClockConfig               
000087c3  TI_memcpy_small                      
000087d5  __TI_decompress_none                 
00008809  DL_Timer_setCaptureCompareValue      
00008819  SYSCFG_DL_SYSTICK_init               
00008829  wcslen                               
00008839  Get_Digtal_For_User                  
00008849  __aeabi_memset                       
00008849  __aeabi_memset4                      
00008849  __aeabi_memset8                      
00008873  TI_memset_small                      
00008881  Sys_GetTick                          
0000888d  Task_CheckNum                        
00008899  DL_Common_delayCycles                
0000892d  SysTick_Handler                      
00008935  __aeabi_errno_addr                   
0000893d  __aeabi_memcpy                       
0000893d  __aeabi_memcpy4                      
0000893d  __aeabi_memcpy8                      
00008963  abort                                
00008969  ADC0_IRQHandler                      
00008969  ADC1_IRQHandler                      
00008969  AES_IRQHandler                       
00008969  CANFD0_IRQHandler                    
00008969  DAC0_IRQHandler                      
00008969  DMA_IRQHandler                       
00008969  Default_Handler                      
00008969  GROUP0_IRQHandler                    
00008969  HardFault_Handler                    
00008969  I2C0_IRQHandler                      
00008969  I2C1_IRQHandler                      
00008969  NMI_Handler                          
00008969  PendSV_Handler                       
00008969  RTC_IRQHandler                       
00008969  SPI0_IRQHandler                      
00008969  SPI1_IRQHandler                      
00008969  SVC_Handler                          
00008969  TIMA0_IRQHandler                     
00008969  TIMA1_IRQHandler                     
00008969  TIMG0_IRQHandler                     
00008969  TIMG12_IRQHandler                    
00008969  TIMG6_IRQHandler                     
00008969  TIMG7_IRQHandler                     
00008969  TIMG8_IRQHandler                     
00008969  UART0_IRQHandler                     
00008969  UART1_IRQHandler                     
00008969  UART2_IRQHandler                     
00008969  UART3_IRQHandler                     
0000896c  C$$EXIT                              
0000896d  HOSTexit                             
00008975  Reset_Handler                        
00008989  _system_pre_init                     
00009586  asc2_1608                            
00009b76  asc2_0806                            
00009da0  __aeabi_ctype_table_                 
00009da0  __aeabi_ctype_table_C                
00009f44  test                                 
0000a01d  reg                                  
0000a134  hw                                   
0000a210  __TI_Handler_Table_Base              
0000a21c  __TI_Handler_Table_Limit             
0000a224  __TI_CINIT_Base                      
0000a234  __TI_CINIT_Limit                     
0000a234  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  GraySensor                           
202003a0  quat                                 
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202003c4  Data_Yaw                             
202003c8  ExISR_Flag                           
202003cc  sensor_timestamp                     
202003d0  sensors                              
202003d2  more                                 
202003d4  Motor_Left                           
2020041c  Motor_Right                          
20200490  Gray_Anolog                          
202004a0  Gray_Normal                          
202004d7  Data_Tracker_Input                   
202004df  Flag_LED                             
202004e0  Motor                                
202004e8  Data_MotorEncoder                    
202004ec  Data_Motor_TarSpeed                  
202004f0  Data_Tracker_Offset                  
202004f4  __aeabi_errno                        
202004f8  delayTick                            
202004fc  uwTick                               
20200504  Flag_MPU6050_Ready                   
20200505  Gray_Digtal                          
20200508  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[332 symbols]
