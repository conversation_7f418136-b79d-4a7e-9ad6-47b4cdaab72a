/**
 * @file system_stability_fix.c
 * @brief 系统稳定性修复方案实现
 * @details 解决中断、延时、初始化导致的系统堵塞和OLED黑屏问题
 * <AUTHOR> Assistant
 * @date 2025-01-01
 */

#include "system_stability_fix.h"
#include "OLED.h"
#include "MPU6050.h"
#include "Task.h"
#include "Serial.h"

/* ========================= 全局变量 ========================= */
system_monitor_t g_system_monitor = {0};
volatile bool g_critical_section_flag = false;

/* ========================= 系统监控功能 ========================= */

/**
 * @brief 系统监控初始化
 */
void SystemMonitor_Init(void)
{
    memset(&g_system_monitor, 0, sizeof(system_monitor_t));
    g_system_monitor.current_state = SYS_STATE_NORMAL;
    g_system_monitor.interrupt_enabled = true;
    g_system_monitor.last_heartbeat = Sys_GetTick();
    
    // 初始化I2C状态
    g_system_monitor.i2c_oled_status.is_locked = false;
    g_system_monitor.i2c_mpu6050_status.is_locked = false;
    
    MyPrintf_DMA("SystemMonitor: Initialized\r\n");
}

/**
 * @brief 系统状态更新
 */
void SystemMonitor_Update(void)
{
    uint32_t current_time = Sys_GetTick();
    
    // 检查心跳超时
    if (current_time - g_system_monitor.last_heartbeat > 5000) {
        g_system_monitor.current_state = SYS_STATE_INTERRUPT_ERROR;
        ErrorHandler_System(SYS_STATE_INTERRUPT_ERROR);
    }
    
    // 检查I2C状态
    if (!I2C_Enhanced_IsHealthy(&g_system_monitor.i2c_oled_status)) {
        g_system_monitor.current_state = SYS_STATE_I2C_ERROR;
    }
    
    // 检查任务超时
    for (uint8_t i = 0; i < MAXTASKS; i++) {
        if (TaskMonitor_CheckTimeout(i)) {
            g_system_monitor.current_state = SYS_STATE_TASK_TIMEOUT;
            ErrorHandler_Task(i);
        }
    }
}

/**
 * @brief 系统心跳
 */
void SystemMonitor_Heartbeat(void)
{
    g_system_monitor.last_heartbeat = Sys_GetTick();
}

/* ========================= I2C增强功能 ========================= */

/**
 * @brief I2C增强写入功能
 */
bool I2C_Enhanced_Write(void* i2c_inst, uint8_t addr, uint8_t* data, uint8_t len)
{
    i2c_status_t* status = (i2c_inst == I2C_OLED_INST) ? 
        &g_system_monitor.i2c_oled_status : &g_system_monitor.i2c_mpu6050_status;
    
    if (status->is_locked) {
        return false; // I2C总线被锁定
    }
    
    uint32_t start_time = Sys_GetTick();
    bool success = false;
    
    for (uint8_t retry = 0; retry < I2C_RETRY_COUNT; retry++) {
        ENTER_CRITICAL_SECTION();
        
        // 检查总线状态
        if (i2c_inst == I2C_OLED_INST) {
            if (DL_I2C_getSDAStatus(I2C_OLED_INST) == DL_I2C_CONTROLLER_SDA_LOW) {
                I2C_OLED_i2c_sda_unlock();
            }
        }
        
        // 执行I2C传输
        DL_I2C_fillControllerTXFIFO(i2c_inst, data, len);
        DL_I2C_clearInterruptStatus(i2c_inst, DL_I2C_INTERRUPT_CONTROLLER_TX_DONE);
        
        while (!(DL_I2C_getControllerStatus(i2c_inst) & DL_I2C_CONTROLLER_STATUS_IDLE)) {
            if (Sys_GetTick() - start_time > I2C_ENHANCED_TIMEOUT_MS) {
                break;
            }
        }
        
        DL_I2C_startControllerTransfer(i2c_inst, addr, DL_I2C_CONTROLLER_DIRECTION_TX, len);
        
        // 等待传输完成
        uint32_t timeout_start = Sys_GetTick();
        while (!DL_I2C_getRawInterruptStatus(i2c_inst, DL_I2C_INTERRUPT_CONTROLLER_TX_DONE)) {
            if (Sys_GetTick() - timeout_start > I2C_ENHANCED_TIMEOUT_MS) {
                break;
            }
        }
        
        if (DL_I2C_getRawInterruptStatus(i2c_inst, DL_I2C_INTERRUPT_CONTROLLER_TX_DONE)) {
            DL_I2C_clearInterruptStatus(i2c_inst, DL_I2C_INTERRUPT_CONTROLLER_TX_DONE);
            success = true;
            status->error_count = 0; // 重置错误计数
            EXIT_CRITICAL_SECTION();
            break;
        }
        
        EXIT_CRITICAL_SECTION();
        
        // 重试前的恢复操作
        I2C_Enhanced_Recovery(i2c_inst, status);
        Delay(5); // 短暂延时后重试
    }
    
    if (!success) {
        status->error_count++;
        status->last_error_time = Sys_GetTick();
        ErrorHandler_I2C(status);
    }
    
    return success;
}

/**
 * @brief I2C总线恢复
 */
void I2C_Enhanced_Recovery(void* i2c_inst, i2c_status_t* status)
{
    status->is_locked = true;
    
    if (i2c_inst == I2C_OLED_INST) {
        I2C_OLED_i2c_sda_unlock();
    } else {
        // MPU6050的I2C恢复（如果需要）
        // mpu6050_i2c_sda_unlock();
    }
    
    Delay(10); // 给总线恢复时间
    status->is_locked = false;
}

/**
 * @brief 检查I2C健康状态
 */
bool I2C_Enhanced_IsHealthy(i2c_status_t* status)
{
    uint32_t current_time = Sys_GetTick();
    
    // 如果错误计数过高且最近有错误，认为不健康
    if (status->error_count > 5 && 
        (current_time - status->last_error_time) < 1000) {
        return false;
    }
    
    return true;
}

/* ========================= 任务监控功能 ========================= */

/**
 * @brief 开始任务监控
 */
void TaskMonitor_Start(uint8_t task_id)
{
    if (task_id >= MAXTASKS) return;
    
    g_system_monitor.task_monitors[task_id].start_time = Sys_GetTick();
    g_system_monitor.task_monitors[task_id].is_running = true;
}

/**
 * @brief 结束任务监控
 */
void TaskMonitor_End(uint8_t task_id)
{
    if (task_id >= MAXTASKS) return;
    
    uint32_t execution_time = Sys_GetTick() - g_system_monitor.task_monitors[task_id].start_time;
    
    if (execution_time > g_system_monitor.task_monitors[task_id].max_execution_time) {
        g_system_monitor.task_monitors[task_id].max_execution_time = execution_time;
    }
    
    g_system_monitor.task_monitors[task_id].is_running = false;
}

/**
 * @brief 检查任务超时
 */
bool TaskMonitor_CheckTimeout(uint8_t task_id)
{
    if (task_id >= MAXTASKS) return false;
    
    task_monitor_t* monitor = &g_system_monitor.task_monitors[task_id];
    
    if (monitor->is_running) {
        uint32_t execution_time = Sys_GetTick() - monitor->start_time;
        if (execution_time > TASK_WATCHDOG_TIMEOUT_MS) {
            monitor->timeout_count++;
            return true;
        }
    }
    
    return false;
}

/* ========================= 中断安全功能 ========================= */

/**
 * @brief 进入临界区
 */
void InterruptSafe_Enter(void)
{
    __disable_irq();
    g_critical_section_flag = true;
}

/**
 * @brief 退出临界区
 */
void InterruptSafe_Exit(void)
{
    g_critical_section_flag = false;
    __enable_irq();
}

/**
 * @brief 检查是否在临界区
 */
bool InterruptSafe_IsInCriticalSection(void)
{
    return g_critical_section_flag;
}

/* ========================= OLED增强功能 ========================= */

/**
 * @brief OLED增强初始化
 */
bool OLED_Enhanced_Init(void)
{
    // 首先检测OLED是否存在
    if (!OLED_IsPresent()) {
        MyPrintf_DMA("OLED: Not present, skipping initialization\r\n");
        return false;
    }
    
    // 使用增强的延时确保稳定性
    Delay_Enhanced(200);
    
    // 执行标准初始化序列，但使用增强的I2C写入
    uint8_t init_commands[] = {
        0xAE, 0x20, 0x10, 0xb0, 0xc8, 0x00, 0x10, 0x40,
        0x81, 0xff, 0xa1, 0xa6, 0xa8, 0x3F, 0xa4, 0xd3,
        0x00, 0xd5, 0xf0, 0xd9, 0x22, 0xda, 0x12, 0xdb,
        0x20, 0x8d, 0x14, 0xaf
    };
    
    for (uint8_t i = 0; i < sizeof(init_commands); i++) {
        uint8_t cmd_data[2] = {0x00, init_commands[i]};
        if (!I2C_Enhanced_Write(I2C_OLED_INST, 0x3C, cmd_data, 2)) {
            MyPrintf_DMA("OLED: Initialization failed at command %d\r\n", i);
            return false;
        }
        Delay_Enhanced(1); // 命令间短暂延时
    }
    
    MyPrintf_DMA("OLED: Enhanced initialization successful\r\n");
    return true;
}

/* ========================= 延时增强功能 ========================= */

/**
 * @brief 增强延时函数（中断安全）
 */
void Delay_Enhanced(uint32_t ms)
{
    uint32_t start = Sys_GetTick();
    while ((Sys_GetTick() - start) < ms) {
        SYSTEM_HEARTBEAT(); // 保持系统心跳
        
        // 检查是否需要处理紧急情况
        if (g_system_monitor.current_state != SYS_STATE_NORMAL) {
            break;
        }
    }
}

/* ========================= 错误处理功能 ========================= */

/**
 * @brief I2C错误处理
 */
void ErrorHandler_I2C(i2c_status_t* status)
{
    MyPrintf_DMA("I2C Error: count=%d, time=%lu\r\n", 
                 status->error_count, status->last_error_time);
    
    if (status->error_count > 10) {
        // 严重错误，执行完整恢复
        SystemRecovery_I2C();
    }
}

/**
 * @brief 任务错误处理
 */
void ErrorHandler_Task(uint8_t task_id)
{
    MyPrintf_DMA("Task %d timeout detected\r\n", task_id);
    TaskMonitor_Reset(task_id);
}

/**
 * @brief 系统错误处理
 */
void ErrorHandler_System(system_state_t error_state)
{
    MyPrintf_DMA("System error: state=%d\r\n", error_state);
    
    switch (error_state) {
        case SYS_STATE_I2C_ERROR:
            SystemRecovery_I2C();
            break;
        case SYS_STATE_TASK_TIMEOUT:
            SystemRecovery_Tasks();
            break;
        case SYS_STATE_INTERRUPT_ERROR:
            SystemRecovery_Interrupts();
            break;
        default:
            SystemRecovery_Full();
            break;
    }
}

/**
 * @brief I2C系统恢复
 */
void SystemRecovery_I2C(void)
{
    MyPrintf_DMA("Recovering I2C subsystem...\r\n");
    
    // 重置I2C状态
    memset(&g_system_monitor.i2c_oled_status, 0, sizeof(i2c_status_t));
    memset(&g_system_monitor.i2c_mpu6050_status, 0, sizeof(i2c_status_t));
    
    // 重新初始化I2C外设
    SYSCFG_DL_I2C_OLED_init();
    SYSCFG_DL_I2C_MPU6050_init();
    
    // 尝试重新初始化OLED
    OLED_Enhanced_Init();
    
    g_system_monitor.current_state = SYS_STATE_RECOVERY;
    Delay_Enhanced(100);
    g_system_monitor.current_state = SYS_STATE_NORMAL;
}

/**
 * @brief 任务监控重置
 */
void TaskMonitor_Reset(uint8_t task_id)
{
    if (task_id >= MAXTASKS) return;
    
    memset(&g_system_monitor.task_monitors[task_id], 0, sizeof(task_monitor_t));
}

/**
 * @brief OLED增强健康检查
 */
bool OLED_Enhanced_IsHealthy(void)
{
    return I2C_Enhanced_IsHealthy(&g_system_monitor.i2c_oled_status);
}

/**
 * @brief OLED增强恢复
 */
void OLED_Enhanced_Recovery(void)
{
    MyPrintf_DMA("OLED: Starting recovery...\r\n");
    I2C_Enhanced_Recovery(I2C_OLED_INST, &g_system_monitor.i2c_oled_status);

    // 尝试重新初始化OLED
    Delay_Enhanced(100);
    if (OLED_Enhanced_Init()) {
        MyPrintf_DMA("OLED: Recovery successful\r\n");
    } else {
        MyPrintf_DMA("OLED: Recovery failed\r\n");
    }
}

/**
 * @brief 系统完整恢复
 */
void SystemRecovery_Full(void)
{
    MyPrintf_DMA("Starting full system recovery...\r\n");

    g_system_monitor.current_state = SYS_STATE_RECOVERY;

    // 1. 恢复中断系统
    SystemRecovery_Interrupts();

    // 2. 恢复I2C系统
    SystemRecovery_I2C();

    // 3. 恢复任务系统
    SystemRecovery_Tasks();

    // 4. 重置系统状态
    Delay_Enhanced(200);
    g_system_monitor.current_state = SYS_STATE_NORMAL;

    MyPrintf_DMA("Full system recovery completed\r\n");
}

/**
 * @brief 中断系统恢复
 */
void SystemRecovery_Interrupts(void)
{
    MyPrintf_DMA("Recovering interrupt subsystem...\r\n");

    // 重新初始化中断
    Interrupt_Init();

    // 重置中断相关标志
    Flag_Serial_RXcplt = false;
    Flag_MPU6050_Ready = false;
    ExISR_Flag = 0;

    g_system_monitor.interrupt_enabled = true;
}

/**
 * @brief 任务系统恢复
 */
void SystemRecovery_Tasks(void)
{
    MyPrintf_DMA("Recovering task subsystem...\r\n");

    // 重置所有任务监控
    for (uint8_t i = 0; i < MAXTASKS; i++) {
        TaskMonitor_Reset(i);
    }
}

/**
 * @brief 系统诊断状态打印
 */
void SystemDiag_PrintStatus(void)
{
    MyPrintf_DMA("=== System Status ===\r\n");
    MyPrintf_DMA("State: %d\r\n", g_system_monitor.current_state);
    MyPrintf_DMA("Heartbeat: %lu\r\n", g_system_monitor.last_heartbeat);
    MyPrintf_DMA("Critical Section: %s\r\n", g_critical_section_flag ? "YES" : "NO");
    MyPrintf_DMA("OLED I2C Errors: %d\r\n", g_system_monitor.i2c_oled_status.error_count);
    MyPrintf_DMA("MPU6050 I2C Errors: %d\r\n", g_system_monitor.i2c_mpu6050_status.error_count);
    MyPrintf_DMA("OLED Locked: %s\r\n", g_system_monitor.i2c_oled_status.is_locked ? "YES" : "NO");
    MyPrintf_DMA("System Uptime: %lu ms\r\n", Sys_GetTick());
}
