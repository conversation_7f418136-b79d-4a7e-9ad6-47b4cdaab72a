#include "SysConfig.h"
#include "system_stability_fix.h"

int main(void)
{
    // 系统初始化（增强版）
    SYSCFG_DL_init();

    // 短暂延时确保硬件稳定
    Delay(100);

    // 项目任务初始化（包含系统监控）
    Task_Init();

    // 初始化完成，打印系统状态
    SystemDiag_PrintStatus();

    // 主循环（增强版）
    uint32_t loop_counter = 0;
    uint32_t last_monitor_update = 0;

    while (1)
    {
        // 执行任务调度
        Task_Start(Sys_GetTick);

        // 定期更新系统监控（每100次循环）
        if (++loop_counter % 100 == 0) {
            SystemMonitor_Update();
            SYSTEM_HEARTBEAT();
        }

        // 每秒打印一次系统状态（用于调试）
        uint32_t current_time = Sys_GetTick();
        if (current_time - last_monitor_update > 5000) { // 5秒间隔
            SystemDiag_PrintStatus();
            last_monitor_update = current_time;
        }

        // 检查系统状态，如果异常则尝试恢复
        if (g_system_monitor.current_state != SYS_STATE_NORMAL &&
            g_system_monitor.current_state != SYS_STATE_RECOVERY) {
            MyPrintf_DMA("System error detected, attempting recovery...\r\n");
            SystemRecovery_Full();
        }
    }
}
